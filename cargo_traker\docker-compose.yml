version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: cargo_tracker_db
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: cargo_tracker
    volumes:
      - mongo_data:/data/db
      - ./backend/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - cargo_network

  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: cargo_tracker_api
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGODB_URI=************************************************************************
    depends_on:
      - mongodb
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - cargo_network

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: cargo_tracker_web
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - cargo_network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: cargo_tracker_cache
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cargo_network

volumes:
  mongo_data:
    driver: local
  redis_data:
    driver: local

networks:
  cargo_network:
    driver: bridge
