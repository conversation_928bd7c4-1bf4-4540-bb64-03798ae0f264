.shipment-details {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 20px 40px;
  min-height: calc(100vh - 80px);
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.details-header h1 {
  color: #2c3e50;
  margin: 0;
  font-size: 2rem;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.info-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.info-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-row label {
  font-weight: 600;
  color: #2c3e50;
  min-width: 120px;
}

.info-row span {
  color: #6c757d;
  text-align: right;
  flex: 1;
}

.info-row .delivered {
  color: #27ae60;
  font-weight: 600;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75em;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-in-transit {
  background-color: #cce5ff;
  color: #004085;
}

.status-delivered {
  background-color: #d4edda;
  color: #155724;
}

.status-delayed {
  background-color: #f8d7da;
  color: #721c24;
}

.status-cancelled {
  background-color: #e2e3e5;
  color: #383d41;
}

.map-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.map-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.tracking-history {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tracking-history h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e0e0e0;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: absolute;
  left: -23px;
  top: 5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #3498db;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #3498db;
}

.timeline-content {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 3px solid #3498db;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeline-header strong {
  color: #2c3e50;
  font-weight: 600;
}

.timeline-date {
  color: #6c757d;
  font-size: 0.9em;
}

.timeline-location {
  color: #3498db;
  font-weight: 500;
  margin-bottom: 5px;
}

.timeline-notes {
  color: #6c757d;
  font-style: italic;
  font-size: 0.9em;
}

.error-container {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin: 20px auto;
  max-width: 500px;
}

.error-container h2 {
  color: #e74c3c;
  margin-bottom: 15px;
}

.error-container p {
  color: #6c757d;
  margin-bottom: 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #7f8c8d;
}

/* Desktop optimizations */
@media (min-width: 1200px) {
  .shipment-details {
    padding: 20px 60px;
  }

  .details-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media (min-width: 769px) and (max-width: 1199px) {
  .shipment-details {
    padding: 20px 30px;
  }
}

@media (max-width: 768px) {
  .shipment-details {
    padding: 10px;
  }
  
  .details-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .details-header h1 {
    font-size: 1.5rem;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .info-card {
    padding: 15px;
  }
  
  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .info-row label {
    min-width: auto;
  }
  
  .info-row span {
    text-align: left;
  }
  
  .timeline {
    padding-left: 25px;
  }
  
  .timeline-marker {
    left: -18px;
  }
  
  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
